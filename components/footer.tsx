import { Scale, Mail, MapPin } from "lucide-react"
import Link from "next/link"

export function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Scale className="h-7 w-7" />
              <span className="text-xl font-bold">FindCarAccidentLawyers</span>
            </div>
            <p className="text-primary-foreground/80 text-base">
              Connecting accident victims with trusted legal representation across the United States.
            </p>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-lg">Quick Links</h4>
            <ul className="space-y-2 text-base">
              <li>
                <Link href="/search" className="hover:text-accent transition-colors">
                  <PERSON>
                </Link>
              </li>
              <li>
                <Link href="/car-accident-settlement-calculator" className="hover:text-accent transition-colors">
                  Free Tools
                </Link>
              </li>
              <li>
                <Link href="/blog" className="hover:text-accent transition-colors">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/about" className="hover:text-accent transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-accent transition-colors">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="hover:text-accent transition-colors">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>



          <div>
            <h4 className="font-semibold mb-4 text-lg">Contact</h4>
            <div className="space-y-2 text-base">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <a href="mailto:<EMAIL>" className="hover:text-accent transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4" />
                <span>Nationwide Service</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center text-base text-primary-foreground/80">
          <p>&copy; 2025 FindCarAccidentLawyers. All rights reserved. |
            <Link href="/privacy" className="hover:text-accent transition-colors ml-1">Privacy Policy</Link> |
            <Link href="/terms" className="hover:text-accent transition-colors ml-1">Terms of Service</Link>
          </p>
        </div>
      </div>
    </footer>
  )
}
