'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calculator, DollarSign, AlertCircle, Info } from "lucide-react"

export function CalculatorForm() {
  const [medicalBills, setMedicalBills] = useState<number>(0)
  const [lostWages, setLostWages] = useState<number>(0)
  const [propertyDamage, setPropertyDamage] = useState<number>(0)
  const [painSuffering, setPainSuffering] = useState<number>(0)
  const [futureMedical, setFutureMedical] = useState<number>(0)
  const [futureLostWages, setFutureLostWages] = useState<number>(0)
  const [emotionalDistress, setEmotionalDistress] = useState<number>(0)
  const [injurySeverity, setInjurySeverity] = useState<string>('moderate')
  const [estimatedSettlement, setEstimatedSettlement] = useState<number | null>(null)
  const [disclaimerAccepted, setDisclaimerAccepted] = useState<boolean>(false)

  // Calculate pain and suffering multiplier based on injury severity
  const getPainSufferingMultiplier = (severity: string) => {
    switch (severity) {
      case 'minor': return 1.5
      case 'moderate': return 2.5
      case 'severe': return 4.0
      case 'catastrophic': return 5.0
      default: return 2.5
    }
  }

  // Calculate settlement based on comprehensive formula
  const calculateSettlement = () => {
    // Base economic damages
    const economicDamages = medicalBills + lostWages + propertyDamage + futureMedical + futureLostWages

    // Pain and suffering calculation
    const multiplier = getPainSufferingMultiplier(injurySeverity)
    const calculatedPainSuffering = painSuffering > 0 ? painSuffering : (economicDamages * multiplier)

    // Total settlement
    const total = economicDamages + calculatedPainSuffering + emotionalDistress

    setEstimatedSettlement(total)
  }

  const resetCalculator = () => {
    setMedicalBills(0)
    setLostWages(0)
    setPropertyDamage(0)
    setPainSuffering(0)
    setFutureMedical(0)
    setFutureLostWages(0)
    setEmotionalDistress(0)
    setInjurySeverity('moderate')
    setEstimatedSettlement(null)
    setDisclaimerAccepted(false)
  }

  const severityOptions = [
    { value: 'minor', label: 'Minor (sprains, bruises)', multiplier: '1.5x' },
    { value: 'moderate', label: 'Moderate (broken bones, concussion)', multiplier: '2.5x' },
    { value: 'severe', label: 'Severe (permanent injury, disability)', multiplier: '4.0x' },
    { value: 'catastrophic', label: 'Catastrophic (life-changing injury)', multiplier: '5.0x' }
  ]

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          Settlement Calculator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Injury Severity */}
        <div className="space-y-2">
          <Label htmlFor="injury-severity" className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            Injury Severity Level
          </Label>
          <Select value={injurySeverity} onValueChange={setInjurySeverity}>
            <SelectTrigger>
              <SelectValue placeholder="Select injury severity" />
            </SelectTrigger>
            <SelectContent>
              {severityOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label} ({option.multiplier})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            This affects pain & suffering calculation when left blank
          </p>
        </div>

        {/* Economic Damages */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Economic Damages</h3>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="medical-bills" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Medical Bills (Past)
              </Label>
              <Input
                id="medical-bills"
                type="number"
                placeholder="0"
                value={medicalBills || ''}
                onChange={(e) => setMedicalBills(Number(e.target.value) || 0)}
                className="text-lg bg-yellow-50 border-yellow-200 focus:bg-yellow-100 focus:border-yellow-300"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="future-medical" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Future Medical Costs
              </Label>
              <Input
                id="future-medical"
                type="number"
                placeholder="0"
                value={futureMedical || ''}
                onChange={(e) => setFutureMedical(Number(e.target.value) || 0)}
                className="text-lg bg-yellow-50 border-yellow-200 focus:bg-yellow-100 focus:border-yellow-300"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="lost-wages" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Lost Wages (Past)
              </Label>
              <Input
                id="lost-wages"
                type="number"
                placeholder="0"
                value={lostWages || ''}
                onChange={(e) => setLostWages(Number(e.target.value) || 0)}
                className="text-lg bg-yellow-50 border-yellow-200 focus:bg-yellow-100 focus:border-yellow-300"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="future-lost-wages" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Future Lost Income
              </Label>
              <Input
                id="future-lost-wages"
                type="number"
                placeholder="0"
                value={futureLostWages || ''}
                onChange={(e) => setFutureLostWages(Number(e.target.value) || 0)}
                className="text-lg bg-yellow-50 border-yellow-200 focus:bg-yellow-100 focus:border-yellow-300"
              />
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="property-damage" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Property Damage
              </Label>
              <Input
                id="property-damage"
                type="number"
                placeholder="0"
                value={propertyDamage || ''}
                onChange={(e) => setPropertyDamage(Number(e.target.value) || 0)}
                className="text-lg bg-yellow-50 border-yellow-200 focus:bg-yellow-100 focus:border-yellow-300"
              />
            </div>
          </div>
        </div>

        {/* Non-Economic Damages */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Non-Economic Damages</h3>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="pain-suffering" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Pain & Suffering
              </Label>
              <Input
                id="pain-suffering"
                type="number"
                placeholder="0"
                value={painSuffering || ''}
                onChange={(e) => setPainSuffering(Number(e.target.value) || 0)}
                className="text-lg bg-yellow-50 border-yellow-200 focus:bg-yellow-100 focus:border-yellow-300"
              />
              <p className="text-xs text-muted-foreground">
                Leave blank to auto-calculate based on severity ({getPainSufferingMultiplier(injurySeverity)}x multiplier)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="emotional-distress" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Emotional Distress
              </Label>
              <Input
                id="emotional-distress"
                type="number"
                placeholder="0"
                value={emotionalDistress || ''}
                onChange={(e) => setEmotionalDistress(Number(e.target.value) || 0)}
                className="text-lg bg-yellow-50 border-yellow-200 focus:bg-yellow-100 focus:border-yellow-300"
              />
            </div>
          </div>
        </div>

        {/* Disclaimer Checkbox */}
        <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <input
              id="disclaimer"
              type="checkbox"
              checked={disclaimerAccepted}
              onChange={(e) => setDisclaimerAccepted(e.target.checked)}
              className="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <Label htmlFor="disclaimer" className="text-sm text-amber-800 cursor-pointer">
              I understand that this calculator provides estimates only and is not legal or financial advice.
              Actual settlement amounts may vary significantly based on specific case details, jurisdiction,
              and other factors. I should consult with qualified attorneys and insurance professionals for
              accurate assessments of my case.
            </Label>
          </div>
        </div>

        <div className="flex gap-4">
          <Button
            onClick={calculateSettlement}
            className="flex-1"
            size="lg"
            disabled={!disclaimerAccepted}
          >
            Calculate Settlement
          </Button>
          <Button onClick={resetCalculator} variant="outline" size="lg">
            Reset
          </Button>
        </div>

        {estimatedSettlement !== null && (
          <div className="mt-6 p-6 bg-primary/5 rounded-lg border-2 border-primary/20">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-primary mb-2">
                Estimated Settlement
              </h3>
              <p className="text-4xl font-bold text-foreground">
                ${estimatedSettlement.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </p>
              <div className="mt-4 flex items-center justify-center gap-2 text-muted-foreground">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">
                  This is an estimate only. Consult an attorney for accurate assessment.
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-semibold mb-2">Calculation Breakdown:</h4>
          <div className="space-y-1 text-sm text-muted-foreground">
            <p>Economic Damages: ${(medicalBills + lostWages + propertyDamage + futureMedical + futureLostWages).toLocaleString()}</p>
            <p>Pain & Suffering: {
              painSuffering > 0
                ? `$${painSuffering.toLocaleString()} (manual input)`
                : `$${(medicalBills + lostWages + propertyDamage + futureMedical + futureLostWages * getPainSufferingMultiplier(injurySeverity)).toLocaleString()} (auto-calculated ${getPainSufferingMultiplier(injurySeverity)}x multiplier)`
            }</p>
            <p>Emotional Distress: ${emotionalDistress.toLocaleString()}</p>
            {estimatedSettlement !== null && (
              <p className="font-semibold text-foreground">
                Total Estimate: ${estimatedSettlement.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
