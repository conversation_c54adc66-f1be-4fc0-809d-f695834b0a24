"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, CheckCircle } from "lucide-react"
import { Camera, Upload, X, Loader2, DollarSign, Download } from "lucide-react"

interface AnalysisResult {
  damageSeverity: string
  estimatedCost: string
  faultIndicators: string[]
  confidence: number
  disclaimer: string
}

export function PhotoAnalyzer() {
  const [files, setFiles] = useState<File[]>([])
  const [previewUrls, setPreviewUrls] = useState<string[]>([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [result, setResult] = useState<AnalysisResult | null>(null)
  const [error, setError] = useState("")
  const [consentGiven, setConsentGiven] = useState(false)
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || [])
    if (files.length + selectedFiles.length > 5) {
      setError("Maximum 5 images allowed")
      return
    }
    const newFiles = [...files, ...selectedFiles.slice(0, 5 - files.length)]
    setFiles(newFiles)

    const newPreviews = newFiles.map(file => URL.createObjectURL(file))
    setPreviewUrls(newPreviews)
    setError("")
  }

  const removeImage = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index)
    const newPreviews = previewUrls.filter((_, i) => i !== index)
    setFiles(newFiles)
    setPreviewUrls(newPreviews)
  }

  const clearAll = () => {
    if (window.confirm('Are you sure you want to clear all uploaded photos and analysis results? This action cannot be undone.')) {
      setFiles([])
      setPreviewUrls([])
      setResult(null)
      setError("")
      setConsentGiven(false)
    }
  }

  const analyzePhotos = async () => {
    if (files.length === 0) {
      setError("Please upload at least one photo")
      return
    }
    if (!consentGiven) {
      setError("Please give consent for image processing")
      return
    }

    setIsAnalyzing(true)
    setError("")

    try {
      const base64Images = await Promise.all(
        files.map(file => {
          return new Promise<string>((resolve) => {
            const reader = new FileReader()
            reader.onload = () => resolve(reader.result as string)
            reader.readAsDataURL(file)
          })
        })
      )

      const content = [
        {
          type: "text",
          text: `Analyze these car accident photos and provide a structured response in the following format:

DAMAGE SEVERITY: [minor/moderate/severe/catastrophic]
ESTIMATED COST: $[amount range]
FAULT INDICATORS:
- [indicator 1]
- [indicator 2]
- [indicator 3]
CONFIDENCE: [percentage]%

Provide detailed analysis for each section. For damage severity, classify as minor (cosmetic damage), moderate (functional damage), severe (structural damage), or catastrophic (total loss). For estimated cost, provide a realistic range in USD. For fault indicators, list specific observable evidence like skid marks, impact patterns, vehicle positioning, etc. IMPORTANT: Emphasize this is NOT legal or medical advice - users should consult professionals.`
        },
        ...base64Images.map(base64 => ({
          type: "image_url" as const,
          image_url: { url: base64 }
        }))
      ]

      const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${process.env.NEXT_PUBLIC_OPENROUTER_API_KEY}`,
          "HTTP-Referer": "https://findcaraccidentlawyers.org",
          "X-Title": "Find Car Accident Attorneys - AI Photo Analyzer",
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          model: "google/gemini-2.5-flash-lite",
          messages: [{
            role: "user",
            content
          }]
        })
      })

      if (!response.ok) {
        throw new Error("Analysis failed")
      }

      const data = await response.json()
      const analysisText = data.choices[0].message.content

      // Parse the response into structured format
      const rawConfidence = parseFloat(extractFromText(analysisText, /confidence.*?(\d+(?:\.\d+)?)/i, "75"))
      const parsedResult: AnalysisResult = {
        damageSeverity: extractFromText(analysisText, /damage\s+severity:\s*([^\n]+)/i, "Unable to determine"),
        estimatedCost: extractFromText(analysisText, /estimated\s+cost:\s*([^\n]+)/i, "Unable to estimate"),
        faultIndicators: extractListFromText(analysisText),
        confidence: clampConfidence(rawConfidence),
        disclaimer: "This analysis is for informational purposes only and is not legal or medical advice. All conclusions are estimates only. Please consult with qualified attorneys and insurance professionals for accurate assessments."
      }

      setResult(parsedResult)
    } catch (err) {
      setError("Analysis failed. Please try again.")
      console.error(err)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const extractFromText = (text: string, regex: RegExp, fallback: string): string => {
    const match = text.match(regex)
    return match ? cleanTextFormatting(match[1].trim()) : fallback
  }

  const cleanTextFormatting = (text: string): string => {
    return text
      .replace(/\*\*/g, '') // Remove bold markdown
      .replace(/\*/g, '') // Remove italic markdown
      .replace(/^\s*[•\-\*]\s*/gm, '') // Remove bullet points
      .replace(/^\s*\d+\)\s*/gm, '') // Remove numbered lists
      .replace(/\n\s*\n/g, '\n') // Remove extra blank lines
      .replace(/\[([^\]]+)\]/g, '$1') // Remove square brackets but keep content
      .trim()
  }

  const clampConfidence = (confidence: number): number => {
    // Clamp confidence between 0 and 1
    return Math.max(0, Math.min(1, confidence / 100)) // Assuming AI might return percentages
  }

  const extractListFromText = (text: string): string[] => {
    // Look for fault indicators section
    const regex = /fault\s+indicators:\s*([\s\S]*?)(?=confidence|$)/i
    const match = text.match(regex)
    if (!match) return ['No specific fault indicators detected']

    // Split by line breaks and clean up
    const items = match[1]
      .split('\n')
      .map(item => cleanTextFormatting(item.trim()))
      .filter(item => item.length > 3 && !item.match(/^[\s\-\*•]+$/)) // Filter out very short items and separator lines
      .slice(0, 10) // Limit to 10 items max

    return items.length > 0 ? items : ['Unable to extract specific indicators']
  }

  const generatePDF = async () => {
    if (!result) return

    setIsGeneratingPDF(true)
    try {
      // Dynamic import to avoid SSR issues
      const jsPDF = (await import('jspdf')).default
      const html2canvas = (await import('html2canvas')).default

      const pdf = new jsPDF()
      const pageWidth = pdf.internal.pageSize.getWidth()
      const pageHeight = pdf.internal.pageSize.getHeight()

      // Header
      pdf.setFontSize(20)
      pdf.setTextColor(0, 0, 0)
      pdf.text('AI Accident Photo Analysis Report', pageWidth / 2, 30, { align: 'center' })

      // Date
      pdf.setFontSize(12)
      pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 50)

      // Damage Severity
      pdf.setFontSize(16)
      pdf.setTextColor(0, 0, 0)
      pdf.text('Damage Severity:', 20, 70)
      pdf.setFontSize(12)
      pdf.text(result.damageSeverity, 20, 85)

      // Confidence
      pdf.text(`Analysis Confidence: ${(result.confidence * 100).toFixed(0)}%`, 20, 100)

      // Estimated Cost
      pdf.setFontSize(16)
      pdf.text('Estimated Repair Cost:', 20, 120)
      pdf.setFontSize(12)
      pdf.text(result.estimatedCost, 20, 135)

      // Fault Indicators
      pdf.setFontSize(16)
      pdf.text('Fault Indicators:', 20, 155)
      pdf.setFontSize(12)
      let yPosition = 170
      result.faultIndicators.forEach((indicator, index) => {
        const lines = pdf.splitTextToSize(`• ${indicator}`, pageWidth - 40)
        pdf.text(lines, 20, yPosition)
        yPosition += lines.length * 7
      })

      // Disclaimer
      yPosition += 10
      pdf.setFontSize(14)
      pdf.setTextColor(200, 0, 0)
      pdf.text('Important Disclaimer:', 20, yPosition)
      yPosition += 15
      pdf.setFontSize(10)
      pdf.setTextColor(0, 0, 0)
      const disclaimerText = 'This analysis is for informational purposes only and is not legal or medical advice. All conclusions are estimates only. Please consult with qualified attorneys and insurance professionals for accurate assessments.'
      const disclaimerLines = pdf.splitTextToSize(disclaimerText, pageWidth - 40)
      pdf.text(disclaimerLines, 20, yPosition)

      // Watermark
      pdf.setFontSize(8)
      pdf.setTextColor(150, 150, 150)
      pdf.text('findcaraccidentlawyers.org', pageWidth / 2, pageHeight - 10, { align: 'center' })

      // Save the PDF
      pdf.save('accident-analysis-report.pdf')
    } catch (error) {
      console.error('Error generating PDF:', error)
      setError('Failed to generate PDF. Please try again.')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle>Upload Accident Photos</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="file-upload" className="cursor-pointer">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-accent transition-colors">
                  <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <div className="text-lg font-medium">Drag & drop photos here</div>
                  <div className="text-sm text-muted-foreground mt-2">Or click to browse files</div>
                </div>
              </Label>
              <Input
                id="file-upload"
                type="file"
                multiple
                accept="image/*"
                className="hidden"
                onChange={handleFileSelect}
              />
            </div>
            <div>
              <Label htmlFor="camera-capture" className="cursor-pointer">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-accent transition-colors">
                  <Camera className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <div className="text-lg font-medium">Take a Photo</div>
                  <div className="text-sm text-muted-foreground mt-2">Open camera on mobile</div>
                </div>
              </Label>
              <input
                id="camera-capture"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={(e) => {
                  const selectedFiles = Array.from(e.target.files || [])
                  if (selectedFiles.length > 0) {
                    handleFileSelect({target: {files: selectedFiles}} as unknown as React.ChangeEvent<HTMLInputElement>)
                  }
                }}
              />
            </div>
          </div>

          {previewUrls.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {previewUrls.map((url, index) => (
                <div key={index} className="relative">
                  <img src={url} alt={`Preview ${index + 1}`} className="w-full h-24 object-cover rounded-lg" />
                  <button
                    onClick={() => removeImage(index)}
                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
            </div>
          )}

          <div className="text-sm text-muted-foreground">
            Upload up to 5 photos (JPG, PNG). Include overall scene, damage details, skid marks, and road conditions for best results.
          </div>
        </CardContent>
      </Card>

      {/* Consent Section */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2">
            <input
              id="consent"
              type="checkbox"
              checked={consentGiven}
              onChange={(e) => setConsentGiven(e.target.checked)}
              className="mr-2 h-4 w-4"
            />
            <Label htmlFor="consent" className="text-sm">
              I consent to processing these images for AI analysis. Images are not stored permanently and processing complies with our Privacy Policy.
            </Label>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-center gap-4">
        <Button
          onClick={analyzePhotos}
          disabled={files.length === 0 || !consentGiven || isAnalyzing}
          size="lg"
          className="px-12"
        >
          {isAnalyzing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Analyzing Photos...
            </>
          ) : (
            "Analyze Photos (AI)"
          )}
        </Button>
        <Button
          onClick={clearAll}
          variant="outline"
          size="lg"
          className="px-8"
          disabled={isAnalyzing}
        >
          Clear All
        </Button>
      </div>

      {/* Error */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
            <span className="font-semibold text-red-700">Error</span>
          </div>
          <p className="mt-2 text-red-600">{error}</p>
        </div>
      )}

      {/* Results */}
      {result && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Analysis Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                <span className="font-semibold text-red-700">Important Disclaimer</span>
              </div>
              <p className="mt-2 text-red-600">{result.disclaimer}</p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="font-semibold mb-3 text-blue-800 flex items-center">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  Damage Severity
                </h3>
                <p className="text-lg font-medium text-blue-900 mb-3">{result.damageSeverity}</p>
                <div className="mt-2">
                  <div className="flex justify-between text-sm text-blue-700 mb-1">
                    <span>Analysis Confidence</span>
                    <span>{(result.confidence * 100).toFixed(0)}%</span>
                  </div>
                  <div className="bg-blue-200 h-2 rounded-full">
                    <div className="bg-blue-600 h-2 rounded-full transition-all duration-300" style={{ width: `${result.confidence * 100}%` }}></div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <h3 className="font-semibold mb-3 text-green-800 flex items-center">
                  <DollarSign className="h-5 w-5 mr-2" />
                  Estimated Repair Cost
                </h3>
                <p className="text-lg font-medium text-green-900">{result.estimatedCost}</p>
                <p className="text-sm text-green-700 mt-2">
                  *Estimates may vary based on location, parts availability, and labor costs
                </p>
              </div>
            </div>

            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <h3 className="font-semibold mb-3 text-orange-800 flex items-center">
                <AlertCircle className="h-5 w-5 mr-2" />
                Fault Indicators Detected
              </h3>
              <div className="space-y-2">
                {result.faultIndicators.map((indicator, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-orange-900 text-sm leading-relaxed">{indicator}</p>
                  </div>
                ))}
              </div>
              <p className="text-xs text-orange-700 mt-3 italic">
                These indicators are observational only and do not constitute legal determination of fault.
              </p>
            </div>

            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-blue-500 mr-2" />
                <span className="font-semibold text-blue-700">Next Steps</span>
              </div>
              <p className="mt-2 text-blue-600">
                Share these results with your attorney or insurance adjuster for proper evaluation. Book a consultation with one of our verified lawyers to discuss your case.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
