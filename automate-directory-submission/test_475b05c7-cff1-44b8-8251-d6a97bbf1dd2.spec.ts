
import { test } from '@playwright/test';
import { expect } from '@playwright/test';

test('Test_2025-09-11', async ({ page, context }) => {
  
    // Navigate to URL
    await page.goto('https://1abc.org/submit/');

    // Click element
    await page.click('input[name='LINK_TYPE'][value='normal']');

    // Fill input field
    await page.fill('input[name='TITLE']', 'Find Car Accident Attorneys - Top-Rated Lawyers Directory');

    // Fill input field
    await page.fill('input[name='URL']', 'https://findcaraccidentlawyers.org/');

    // Fill input field
    await page.fill('textarea[name='DESCRIPTION']', 'Find the best car accident attorneys in your area with our comprehensive directory. Browse verified reviews, ratings, and contact information for top-rated personal injury lawyers specializing in car accidents, auto accidents, and collision cases. Get expert legal representation when you need it most - free consultations available.');

    // Fill input field
    await page.fill('input[name='OWNER_NAME']', 'Jennifer');

    // Fill input field
    await page.fill('input[name='OWNER_EMAIL']', '<EMAIL>');

    // Select option
    await page.selectOption('select[name='CATEGORY_ID']', '67');

    // Click element
    await page.click('input[type='submit']');
});